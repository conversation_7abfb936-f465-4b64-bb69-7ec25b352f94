import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';

/// نموذج لتتبع جلسات الأجهزة
class DeviceSessionModel {
  final String deviceId;
  final String deviceName;
  final String platform;
  final String userId;
  final DateTime loginTime;
  final DateTime lastActivity;
  final bool isActive;
  final String sessionId;

  DeviceSessionModel({
    required this.deviceId,
    required this.deviceName,
    required this.platform,
    required this.userId,
    required this.loginTime,
    required this.lastActivity,
    required this.isActive,
    required this.sessionId,
  });

  /// تحويل من Map إلى DeviceSessionModel
  factory DeviceSessionModel.fromMap(Map<String, dynamic> map) {
    return DeviceSessionModel(
      deviceId: map['deviceId'] ?? '',
      deviceName: map['deviceName'] ?? '',
      platform: map['platform'] ?? '',
      userId: map['userId'] ?? '',
      loginTime: DateTime.parse(map['loginTime'] ?? DateTime.now().toIso8601String()),
      lastActivity: DateTime.parse(map['lastActivity'] ?? DateTime.now().toIso8601String()),
      isActive: map['isActive'] ?? false,
      sessionId: map['sessionId'] ?? '',
    );
  }

  /// تحويل من DeviceSessionModel إلى Map
  Map<String, dynamic> toMap() {
    return {
      'deviceId': deviceId,
      'deviceName': deviceName,
      'platform': platform,
      'userId': userId,
      'loginTime': loginTime.toIso8601String(),
      'lastActivity': lastActivity.toIso8601String(),
      'isActive': isActive,
      'sessionId': sessionId,
    };
  }

  /// إنشاء نسخة محدثة من النموذج
  DeviceSessionModel copyWith({
    String? deviceId,
    String? deviceName,
    String? platform,
    String? userId,
    DateTime? loginTime,
    DateTime? lastActivity,
    bool? isActive,
    String? sessionId,
  }) {
    return DeviceSessionModel(
      deviceId: deviceId ?? this.deviceId,
      deviceName: deviceName ?? this.deviceName,
      platform: platform ?? this.platform,
      userId: userId ?? this.userId,
      loginTime: loginTime ?? this.loginTime,
      lastActivity: lastActivity ?? this.lastActivity,
      isActive: isActive ?? this.isActive,
      sessionId: sessionId ?? this.sessionId,
    );
  }

  /// الحصول على معرف فريد للجهاز
  static Future<String> getDeviceId() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      
      if (kIsWeb) {
        final webInfo = await deviceInfo.webBrowserInfo;
        return webInfo.userAgent ?? 'web_unknown';
      } else if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return androidInfo.id;
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return iosInfo.identifierForVendor ?? 'ios_unknown';
      } else if (Platform.isWindows) {
        final windowsInfo = await deviceInfo.windowsInfo;
        return windowsInfo.computerName;
      } else if (Platform.isMacOS) {
        final macInfo = await deviceInfo.macOsInfo;
        return macInfo.systemGUID ?? 'mac_unknown';
      } else if (Platform.isLinux) {
        final linuxInfo = await deviceInfo.linuxInfo;
        return linuxInfo.machineId ?? 'linux_unknown';
      }
      
      return 'unknown_device';
    } catch (e) {
      debugPrint('خطأ في الحصول على معرف الجهاز: $e');
      return 'error_device_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// الحصول على اسم الجهاز
  static Future<String> getDeviceName() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      
      if (kIsWeb) {
        final webInfo = await deviceInfo.webBrowserInfo;
        return '${webInfo.browserName} على ${webInfo.platform}';
      } else if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return '${androidInfo.brand} ${androidInfo.model}';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return '${iosInfo.name} (${iosInfo.model})';
      } else if (Platform.isWindows) {
        final windowsInfo = await deviceInfo.windowsInfo;
        return 'Windows - ${windowsInfo.computerName}';
      } else if (Platform.isMacOS) {
        final macInfo = await deviceInfo.macOsInfo;
        return 'macOS - ${macInfo.computerName}';
      } else if (Platform.isLinux) {
        final linuxInfo = await deviceInfo.linuxInfo;
        return 'Linux - ${linuxInfo.name}';
      }
      
      return 'جهاز غير معروف';
    } catch (e) {
      debugPrint('خطأ في الحصول على اسم الجهاز: $e');
      return 'جهاز غير معروف';
    }
  }

  /// الحصول على منصة الجهاز
  static String getPlatform() {
    if (kIsWeb) {
      return 'Web';
    } else if (Platform.isAndroid) {
      return 'Android';
    } else if (Platform.isIOS) {
      return 'iOS';
    } else if (Platform.isWindows) {
      return 'Windows';
    } else if (Platform.isMacOS) {
      return 'macOS';
    } else if (Platform.isLinux) {
      return 'Linux';
    }
    
    return 'Unknown';
  }

  /// إنشاء معرف جلسة فريد
  static String generateSessionId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString().padLeft(4, '0');
    return 'session_${timestamp}_$random';
  }

  @override
  String toString() {
    return 'DeviceSessionModel(deviceId: $deviceId, deviceName: $deviceName, platform: $platform, userId: $userId, loginTime: $loginTime, lastActivity: $lastActivity, isActive: $isActive, sessionId: $sessionId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is DeviceSessionModel &&
        other.deviceId == deviceId &&
        other.sessionId == sessionId;
  }

  @override
  int get hashCode {
    return deviceId.hashCode ^ sessionId.hashCode;
  }
}
